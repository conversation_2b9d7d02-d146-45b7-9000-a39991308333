["blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_auth_error", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_network_error", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_not_found_error", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_with_auth_success", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_custom", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_defaults", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_fallback", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_git_clone_enabled", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_invalid_url", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_oauth", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_pat", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_basic", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_with_special_chars", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging", "blitzy_utils/test/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging_no_auth", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_auth_error", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_network_error", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_not_found_error", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_with_auth_success", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_custom", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_defaults", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_fallback", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_git_clone_enabled", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_invalid_url", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_oauth", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_pat", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_basic", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_with_special_chars", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging", "blitzy_utils/test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging_no_auth", "test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_auth_error", "test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_network_error", "test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_not_found_error", "test_azure_git_clone.py::TestAzureDevOpsGitClone::test_clone_azure_devops_repository_with_auth_success", "test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_custom", "test_azure_git_clone.py::TestAzureDevOpsGitCloneConfig::test_get_azure_devops_git_clone_config_defaults", "test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_fallback", "test_azure_git_clone.py::TestAzureDevOpsIntegration::test_download_all_git_files_with_git_clone_enabled", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_invalid_url", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_oauth", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_add_auth_to_azure_devops_url_with_pat", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_basic", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_build_azure_devops_clone_url_with_special_chars", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging", "test_azure_git_clone.py::TestAzureDevOpsUrlUtils::test_sanitize_url_for_logging_no_auth"]